#!/bin/bash
# Setup script for AI Docker environment

set -e # Exit immediately if a command exits with a non-zero status.

# Parse command line arguments
DELETE_IMAGES=false # Default to false
while [[ $# -gt 0 ]]; do
  case $1 in
    --delete-images) # New argument
      DELETE_IMAGES=true
      shift
      ;;
    --download-auth) # Keep for backward compatibility but show warning
      echo "Warning: --download-auth flag is deprecated and will be ignored."
      echo "The account rotation system now handles authentication automatically."
      shift
      ;;
    *)
      echo "Unknown option: $1"
      echo "Usage: $0 [--delete-images]" # Updated usage
      echo "  --delete-images: Delete existing Docker images (disabled by default)" # Added description

      ;;
  esac
done

# Define directories and file paths on the host
# Use current directory instead of HOME
HAR_DIR="./har_and_cookies"
IMAGES_DIR="./generated_media"
# Authentication files directory
AUTH_FILES_DIR="./authentication_files"
TARGET_UID=1200
TARGET_GID=1201
# Define MySQL Credentials
DB_USER="think" # User for database (previously for new-api)
DB_PASSWORD="pL3xS3cur3Us3rP@ssw0rd!" # Password for database user (previously for new-api)
MYSQL_ROOT_PASSWORD_VAR="pL3xS3cur3DBP@ssw0rd!" # Root password

# Define LiteLLM PostgreSQL Credentials
LITELLM_DB_NAME="litellm"
LITELLM_DB_USER="think"
LITELLM_DB_PASSWORD="Str0ngLit3LLMP@ssw0rd!"
LITELLM_MASTER_KEY_VAR="sk-aK3jS7bR9pL2xW8vC4mN0fG6dZ1hY5gQ"

# Stop and remove existing containers and clean up images
echo "Cleaning up existing containers..."
kill $(pgrep -f auth_updater_service.sh) 2>/dev/null || true

# Check if docker-compose.yml exists before running docker compose down
if [ -f "docker-compose.yml" ]; then
  docker compose down --remove-orphans 2>/dev/null || echo "Warning: docker compose down failed, continuing anyway..."
else
  echo "No docker-compose.yml found, skipping docker compose down"
fi

# Stop and remove containers individually
docker stop open-webui ai-g4fcs redis watchtower ai-g4fcs-updater 2>/dev/null || true
docker rm open-webui ai-g4fcs redis watchtower ai-g4fcs-updater 2>/dev/null || true
# Commented out MySQL container as it's no longer used
# docker stop mysql 2>/dev/null || true
# docker rm mysql 2>/dev/null || true
# Commented out new-api container
# docker stop new-api 2>/dev/null || true
# docker rm new-api 2>/dev/null || true
# Commented out ChatNio container
# docker stop chatnio 2>/dev/null || true
# docker rm chatnio 2>/dev/null || true

# Conditionally delete images
if [ "$DELETE_IMAGES" = true ]; then
  echo "Deleting existing images..."
  docker rmi ghcr.io/thinkfar/ai/g4fcs:main ghcr.io/open-webui/open-webui:main redis:latest containrrr/watchtower 2>/dev/null || true
  # Commented out MySQL image as it's no longer used
  # docker rmi mysql:8.2 2>/dev/null || true
  # Commented out new-api image
  # docker rmi calciumion/new-api:latest 2>/dev/null || true
  # Commented out ChatNio image
  # docker rmi programzmh/chatnio:latest 2>/dev/null || true
  echo "Existing images deleted."
else
  echo "Skipping image deletion (use --delete-images to enable)."
fi

# Create mysql-init.sql file to initialize chatnio database
# Commented out ChatNio-related mysql-init.sql file creation
# echo "Creating mysql-init.sql file..."
# cat << EOFSQL > mysql-init.sql
# -- Initialize chatnio database
# -- Commented out ChatNio database creation
# -- CREATE DATABASE IF NOT EXISTS chatnio;
# EOFSQL
# echo "mysql-init.sql created."

# Create litellm_config.yaml file for Redis integration
echo "Creating litellm_config.yaml file..."
cat << EOFLEC > litellm_config.yaml
# LiteLLM Configuration File

router_settings:
  # Redis for sharing TPM/RPM across multiple litellm instances (if scaled).
  redis_host: "redis"
  redis_port: 6379
  # redis_password: null # Set if your Redis has a password.

litellm_settings:
  # callbacks: ["prometheus"] # Prometheus metrics integration - disabled
  cache: True
  cache_params:
    type: "redis"       # Enable Redis caching.
    host: "redis"
    port: 6379
  # service_callbacks: ["prometheus_system"] # System health metrics for Redis/Postgres via LiteLLM - disabled
  request_timeout: 43200    # raise Timeout error if call takes longer than 600 seconds. Default value is 6000 seconds if not set
  set_verbose: False      # Switch off Debug Logging, ensure your logs do not have any debugging on
  # json_logs: true         # Get debug logs in json format
  check_provider_endpoint: true  # Enable checking provider endpoints for wildcard models

general_settings:
  # master_key is set via LITELLM_MASTER_KEY environment variable in docker-compose.yml
  store_model_in_db: True # Store model configurations in the database.
  proxy_batch_write_at: 60 # Batch write spend updates every 60s
  # disable_spend_logs: True # turn off writing each transaction to the db. We recommend doing this is you don't need to see Usage on the LiteLLM UI and are tracking metrics via Prometheus (optional)
  # disable_error_logs: True # turn off writing LLM Exceptions to DB

EOFLEC
echo "litellm_config.yaml created."

# Define the docker-compose.yml content using a HEREDOC
cat << EOFDC > docker-compose.yml
services:
  # --- AI Gateway & Proxy Service ---
  ai-g4fcs:
    image: ghcr.io/thinkfar/ai/g4fcs:main
    container_name: ai-g4fcs
    restart: unless-stopped # Ensures the service restarts unless manually stopped
    shm_size: "3g" # Shared memory size, can be important for some AI model operations
    volumes:
      - ./har_and_cookies:/app/har_and_cookies # Mounts host directory for HAR files and cookies, used for account interaction
      - ./generated_media:/app/generated_media # Mounts host directory for any media generated by underlying models
      - ./authentication_files:/app/authentication_files # Mounts host directory for authentication credentials
    environment:
      - PROXY_DOMAIN=ai-px-cs.snacne.com # Domain for the proxy service
      # - G4F_LOGIN_URL=... # Optional: URL for automated login if needed
      # Account rotation configuration for managing multiple AI service accounts
      - REMOTE_ACCOUNTS_URL=https://gist.github.com/ThinkFar/4b61378f3e46ca8d5a86e5da669ffe51/raw/auth_g4fcs.json # URL to fetch account credentials
      - RATE_LIMIT_ROTATION_ENABLED=true  # Enables automatic account rotation upon hitting rate limits
      - ROTATION_STRATEGY=round-robin # Strategy for selecting the next account
      - REMOTE_CHECK_INTERVAL=10 # Interval in seconds to check for remote account updates
      # Memory management configuration for the service
      - MAX_REQUEST_TIMES=1000
      - MAX_RATE_LIMITED_ACCOUNTS=1000
      - MAX_PENDING_CHANGES=1000
    ports:
      - "8082:8082" # Exposes the URL masking proxy port
      - "8084:8084" # Exposes the account metrics API port

  # --- Primary API Service (e.g., for Open WebUI) ---
  # Commented out new-api service
#  new-api:
#    image: calciumion/new-api:latest
#    container_name: new-api
#    restart: unless-stopped # Ensures the service restarts unless manually stopped
#    command: --log-dir /app/logs # Specifies the command to run and a custom log directory
#    ports:
#      - "3000:3000" # Exposes the API service port (e.g., for Open WebUI to connect to)
#    volumes:
#      - ./data:/data # Mounts host directory for persistent API data
#      - ./logs:/app/logs # Mounts host directory for API logs
#    environment:
#      - SQL_DSN=${DB_USER}:${DB_PASSWORD}@tcp(mysql:3306)/new-api # Data Source Name for MySQL connection
#      - REDIS_CONN_STRING=redis://redis # Connection string for Redis
#      - TZ=UTC # Sets the timezone to UTC
#    depends_on:
#      redis: # Ensures Redis starts before new-api
#        condition: service_started
#      mysql: # Ensures MySQL is healthy before new-api starts
#        condition: service_healthy
#      ai-g4fcs: # Ensures ai-g4fcs (proxy) starts before new-api, if it acts as an upstream
#        condition: service_started
#    healthcheck: # Defines how to check if the service is healthy
#      test: >
#        wget -q -O - http://localhost:3000/api/status | grep -q '"success":\\s*true' || exit 1
#      interval: 30s # How often to run the health check
#      timeout: 10s  # How long to wait for a response
#      retries: 3    # How many times to retry if it fails

  # --- Web UI for LLMs (like ChatGPT) ---
  open-webui:
    image: ghcr.io/open-webui/open-webui:main
    container_name: open-webui
    restart: unless-stopped # Ensures the service restarts unless manually stopped
    ports:
      - "8080:8080" # Exposes the Open WebUI interface (changed from 3000 to avoid conflict with new-api)
    volumes:
      - open-webui_data:/app/backend/data # Uses a named volume for persistent Open WebUI data
#    depends_on:
#      - new-api # Ensures new-api (which it uses as a backend) is started
    # environment: # Example environment variable for connecting to a backend LLM API
    #   - OPENAI_API_BASE_URL=http://new-api:3000/v1

  # --- Alternative Chat Interface ---
  # Commented out ChatNio service
  # chatnio:
  #   image: programzmh/chatnio:latest
  #   container_name: chatnio
  #   restart: unless-stopped # Ensures the service restarts unless manually stopped
  #   ports:
  #     - "8085:8094" # Exposes the ChatNio web interface
  #   ulimits: # Sets resource limits for the container
  #     nofile:
  #       soft: 65535
  #       hard: 65535
  #   volumes:
  #     - ./chatnio/config:/config # Mounts host directory for ChatNio configuration
  #     - ./chatnio/logs:/logs     # Mounts host directory for ChatNio logs
  #     - ./chatnio/storage:/storage # Mounts host directory for ChatNio storage
  #   environment:
  #     - MYSQL_HOST=mysql # Hostname for the MySQL database
  #     - MYSQL_PORT=3306 # Port for the MySQL database
  #     - MYSQL_DB=chatnio # Specifies the database ChatNio needs
  #     - MYSQL_USER=root # MySQL user (consider a less privileged user for production)
  #     - MYSQL_PASSWORD=${MYSQL_ROOT_PASSWORD_VAR} # MySQL root password (from shell variable)
  #     - REDIS_HOST=redis # Hostname for Redis
  #     - REDIS_PORT=6379 # Port for Redis
  #     - SERVE_STATIC=true # Instructs ChatNio to serve static assets
  #   depends_on:
  #     mysql: # Ensures MySQL is healthy before ChatNio starts
  #       condition: service_healthy
  #     redis: # Ensures Redis starts before ChatNio
  #       condition: service_started
  #   links: # Legacy way to link containers, depends_on is generally preferred
  #     - mysql
  #     - redis

  # --- In-Memory Data Store (Cache, Message Broker) ---
  redis:
    image: redis:latest
    container_name: redis
    restart: unless-stopped # Ensures the service restarts unless manually stopped
    # No ports exposed externally, accessed by other services on the internal Docker network

  # --- Relational Database Service ---
  # Commented out MySQL service as it's no longer needed after removing new-api
#  mysql:
#    image: mysql:8.2
#    container_name: mysql
#    restart: unless-stopped # Ensures the service restarts unless manually stopped
#    environment:
#      MYSQL_USER: ${DB_USER} # Creates a non-root user (from shell variable)
#      MYSQL_PASSWORD: ${DB_PASSWORD} # Password for the non-root user (from shell variable)
#      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD_VAR} # Root password for MySQL (from shell variable)
##      MYSQL_DATABASE: new-api # Creates an initial database for new-api
#    volumes:
#      - mysql_data:/var/lib/mysql # Uses a named volume for persistent MySQL data
#      # Commented out ChatNio-related initialization script
#      # - ./mysql-init.sql:/docker-entrypoint-initdb.d/mysql-init.sql # Runs this SQL script on initialization (e.g., to create chatnio DB)
#    healthcheck: # Defines how to check if MySQL is healthy
#      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-uroot", "-p${MYSQL_ROOT_PASSWORD_VAR}"]
#      interval: 10s
#      timeout: 5s
#      retries: 20
#      start_period: 30s # Gives MySQL time to start up before health checks begin
#    # No ports exposed externally, accessed by other services on the internal Docker network

  # --- Docker Container Auto-Updater ---
  watchtower:
    image: containrrr/watchtower
    container_name: watchtower
    restart: always # Ensures watchtower is always running to check for updates
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock # Allows watchtower to interact with the Docker daemon
    command: --interval 3600 --stop-timeout 60s # Checks for new images every hour (3600s) and gives containers 60s to stop

  # --- LiteLLM Proxy, DB, and Prometheus ---

  # --- LiteLLM: Universal LLM API Gateway ---
  litellm:
    image: ghcr.io/berriai/litellm:main-latest # Specifies the LiteLLM image to use
    container_name: litellm # Added for consistency
    restart: unless-stopped # Added for consistency
    volumes:
     - ./litellm_config.yaml:/app/config.yaml # Mounts custom LiteLLM configuration file
    command:
     - "--config=/app/config.yaml"    # Tells LiteLLM to use the custom config
    ports:
      - "4000:4000" # Exposes LiteLLM API endpoint and UI
    environment:
      # Connects LiteLLM to its PostgreSQL database
      DATABASE_URL: "postgresql://${LITELLM_DB_USER}:${LITELLM_DB_PASSWORD}@postgres:5432/${LITELLM_DB_NAME}"
      LITELLM_MASTER_KEY: "${LITELLM_MASTER_KEY_VAR}" # Master key for LiteLLM Admin UI and API key generation.
    # If you have API keys, create a .env file and uncomment the following lines
    # env_file:
    #   - ./.env # Load environment variables from a .env file (for API keys)
    depends_on:
      postgres: # Ensures the PostgreSQL database starts before LiteLLM
        condition: service_started
      redis: # Ensures Redis starts before LiteLLM
        condition: service_started
      ai-g4fcs: # Ensures the GPT4Free container starts before LiteLLM
        condition: service_started
    healthcheck: # Defines how to check if LiteLLM is healthy
      test: [
          "CMD",
          "curl",
          "-f",
          "http://localhost:4000/health/liveliness || exit 1", # Checks the liveliness endpoint
        ]
      interval: 30s
      timeout: 10s
      retries: 6
      start_period: 40s # Gives LiteLLM time to start before health checks begin

  # --- PostgreSQL Database for LiteLLM ---
  postgres: # This is the PostgreSQL database service for LiteLLM
    image: postgres:16
    restart: always # Ensures the database is always running
    container_name: litellm_db # Specific container name for the LiteLLM database
    environment:
      POSTGRES_DB: ${LITELLM_DB_NAME} # Sets the database name (from shell variable)
      POSTGRES_USER: ${LITELLM_DB_USER} # Sets the database user (from shell variable)
      POSTGRES_PASSWORD: ${LITELLM_DB_PASSWORD} # Sets the database password (from shell variable)
    # ports:
    #   - "5432:5432" # Exposes PostgreSQL port (standard port) - primarily for external debugging/management if needed
    volumes:
      - postgres_data:/var/lib/postgresql/data # Uses a named volume for persistent PostgreSQL data
    healthcheck: # Defines how to check if PostgreSQL is ready
      test: ["CMD-SHELL", "pg_isready -d ${LITELLM_DB_NAME} -U ${LITELLM_DB_USER}"] # Checks if the DB is accepting connections
      interval: 1s
      timeout: 5s
      retries: 10

  # --- Prometheus: Monitoring System --- (removed from setup)
#  prometheus:
#    image: prom/prometheus
#    container_name: prometheus # Specific container name for Prometheus
#    volumes:
#      - prometheus_data:/prometheus # Uses a named volume for persistent Prometheus data (metrics storage)
#      - ./prometheus.yml:/etc/prometheus/prometheus.yml # Mounts the Prometheus configuration file from the host
#    ports:
#      - "8099:9090" # Exposes the Prometheus web UI and API
#    command:
#      - "--config.file=/etc/prometheus/prometheus.yml" # Tells Prometheus to use the mounted config file
#      - "--storage.tsdb.path=/prometheus" # Specifies the path for time series database storage
#      - "--storage.tsdb.retention.time=15d" # Sets metrics retention to 15 days
#    restart: always # Ensures Prometheus is always running

volumes:
  # Named volume for MySQL data persistence (commented out as MySQL is no longer used)
  # mysql_data:
  # Named volume for Open WebUI data persistence
  open-webui_data:
  # Named volume for Prometheus data persistence (not used)
#  prometheus_data:
#    driver: local # Specifies the local driver for this volume
  # Named volume for PostgreSQL data persistence (for LiteLLM)
  postgres_data:
    name: litellm_postgres_data # Explicitly names the volume on the host
EOFDC

echo "docker-compose.yml created."

# Create prometheus.yml file (skipped - not needed)
# echo "Creating prometheus.yml file..."
# cat << EOFPML > prometheus.yml
# global:
#   scrape_interval: 15s
#
# scrape_configs:
#   - job_name: 'prometheus'
#     static_configs:
#       - targets: ['localhost:9090']
#   - job_name: 'litellm'
#     metrics_path: /metrics  # LiteLLM metrics endpoint as per official documentation
#     static_configs:
#       - targets: ['litellm:4000']
#
# EOFPML
# echo "prometheus.yml created."

# --- Host Setup Steps ---

# Function to check if running inside an Incus or LXC container
is_in_container() {
  if [ -f "/run/systemd/container" ] || grep -q "lxc\|incus" /proc/1/environ 2>/dev/null; then
    # Check the content of /run/systemd/container if it exists
    local container_type=$(cat /run/systemd/container 2>/dev/null)
    if [ -n "$container_type" ]; then
      echo "Detected container type: $container_type"
    else
      echo "Detected container environment"
    fi
    return 0
  else
    return 1
  fi
}

# Function to handle sudo commands with proper error handling
run_sudo_command() {
  local cmd="$1"
  local error_msg="$2"

  # Try running the command with sudo
  if sudo -n true 2>/dev/null; then
    # Non-interactive sudo is available
    if eval "sudo $cmd"; then
      return 0
    fi
  else
    # Non-interactive sudo is not available, try with regular sudo
    echo "Sudo requires password. If this script is running non-interactively, please run it with sudo."
    if eval "sudo $cmd"; then
      return 0
    fi
  fi

  # If we get here, the command failed
  echo "$error_msg"
  return 1
}

# 1. Create host directories with current user permissions first
echo "Creating host directories..."
mkdir -p "${HAR_DIR}" "${IMAGES_DIR}" "${AUTH_FILES_DIR}"
echo "Directories created: ${HAR_DIR}, ${IMAGES_DIR}, ${AUTH_FILES_DIR}"

# Set very permissive permissions on directories to avoid permission issues
chmod 777 "${HAR_DIR}" "${IMAGES_DIR}" "${AUTH_FILES_DIR}"
echo "Directory permissions set to 777 for maximum compatibility"

# 2. Create necessary directories for account rotation
echo "Creating necessary directories for account rotation..."
mkdir -p "${HAR_DIR}"
mkdir -p "${IMAGES_DIR}"
mkdir -p "${AUTH_FILES_DIR}"

echo "The account rotation system will handle authentication automatically."
echo "No manual auth file download is needed."

# 4. Set host directory ownership (requires sudo)
echo "Setting ownership for host directories..."

# Check if we're running inside a container
if is_in_container; then
  echo "Running inside a container - using container-specific permissions..."
  # Inside container: use PWD-based paths and direct sudo command
  if sudo chown -R 1200:1201 "${PWD}/har_and_cookies" "${PWD}/generated_media" "${PWD}/authentication_files"; then
    echo "Directory ownership set to 1200:1201 (container mode)."
  else
    echo "Directory ownership could not be set in container mode."
    echo "The containers should still be able to access the directories with 777 permissions."
  fi
else
  echo "Running outside a container - using standard permissions..."
  # Outside container: use the original approach
  if run_sudo_command "chown -R ${TARGET_UID}:${TARGET_GID} \"${HAR_DIR}\" \"${IMAGES_DIR}\" \"${AUTH_FILES_DIR}\"" "Warning: Failed to set directory ownership, but this is not critical with 777 permissions."; then
    echo "Directory ownership set to ${TARGET_UID}:${TARGET_GID}."
  else
    echo "Directory ownership could not be set, but this is not critical with 777 permissions."
    echo "The containers should still be able to access the directories."
  fi
fi

# 5. Account rotation system will handle authentication
echo "The account rotation system will handle authentication automatically."
echo "No auth file updater service is needed."

# --- Completion ---

echo ""
echo "Setup complete!"
echo "Starting services automatically with: docker compose up -d --pull always"
# Create empty .env file if it doesn't exist to avoid docker-compose errors
touch .env
if docker compose up -d --pull always; then
  echo "Docker services started successfully"
else
  echo "Warning: Failed to start Docker services. Please check Docker installation and try again manually with 'docker compose up -d'"
fi

echo ""
echo "Services should be starting in the background."
echo ""
echo "You can access the services at:"
echo "- AI G4FCS Masking Proxy: http://localhost:8082"
echo "- AI G4FCS Completions Proxy: http://ai-g4fcs:8081/v1"
echo "- Open WebUI: http://localhost:8080"
# Commented out New API service
# echo "- New API: http://localhost:3000"
# Commented out ChatNio service
# echo "- ChatNio: http://localhost:8085"
echo "- LiteLLM Proxy: http://localhost:4000"
# echo "- Prometheus: http://localhost:8099" # Prometheus service removed
# Commented out New API initialization note
# echo "Note: The New API service needs to be initialized on first run."
# echo "Visit http://localhost:3000 to complete the setup."
echo ""

echo "The account rotation system will automatically handle authentication."
echo "It will fetch accounts from the remote source and rotate them as needed."
echo "No manual auth file download or updater service is needed."