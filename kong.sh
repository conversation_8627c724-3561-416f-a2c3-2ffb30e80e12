#!/bin/bash
set -euo pipefail

# Kong Robust Setup Script - All-in-One Implementation
# Includes all recommended waiting strategies and robust features

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}INFO:${NC} $1"
}

log_success() {
    echo -e "${GREEN}SUCCESS:${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}WARNING:${NC} $1"
}

log_error() {
    echo -e "${RED}ERROR:${NC} $1"
}

# --- Configuration ---
BASE_DIR="kong"
POSTGRES_PASSWORD="$(openssl rand -base64 16)"

# Robust Waiting Configuration
MAX_ATTEMPTS=30
BASE_DELAY=1
MAX_DELAY=10
HEALTH_CHECK_TIMEOUT=300  # Increased to 5 minutes for initial setup

# Docker Image Versions
export KONG_VERSION="${KONG_VERSION:-latest}"
export POSTGRES_VERSION="${POSTGRES_VERSION:-16}"
export REDIS_VERSION="${REDIS_VERSION:-latest}"

# Environment Detection and Domain Configuration
DEPLOYMENT_MODE="${DEPLOYMENT_MODE:-auto}"

# Auto-detect deployment mode if not explicitly set
if [ "$DEPLOYMENT_MODE" = "auto" ]; then
    # Check if we're in a local development environment
    if [ -n "${CI:-}" ] || [ -n "${GITHUB_ACTIONS:-}" ] || [ -n "${GITLAB_CI:-}" ]; then
        DEPLOYMENT_MODE="production"
    elif [ -f "/.dockerenv" ] || [ -n "${CONTAINER:-}" ]; then
        DEPLOYMENT_MODE="production"
    else
        DEPLOYMENT_MODE="local"
    fi
fi

log_info "Detected deployment mode: $DEPLOYMENT_MODE"

# Domain Configuration based on deployment mode
if [ "$DEPLOYMENT_MODE" = "local" ]; then
    export KONG_DOMAIN="${KONG_DOMAIN:-http://localhost:8002}"
    export API_DOMAIN="${API_DOMAIN:-http://localhost:8000/admin-api}"
    log_info "Using local development URLs for Kong Manager"
else
    export KONG_DOMAIN="${KONG_DOMAIN:-https://kong.seplin.co}"
    export API_DOMAIN="${API_DOMAIN:-https://kong.seplin.co/admin-api}"
    log_info "Using production URLs for Kong Manager"
fi

# Export Kong database configuration
export KONG_DATABASE="${KONG_DATABASE:-postgres}"
export KONG_PG_DATABASE="${KONG_PG_DATABASE:-kong}"
export KONG_PG_USER="${KONG_PG_USER:-kong}"
export KONG_PREFIX="${KONG_PREFIX:-/var/run/kong}"
export KONG_NGINX_WORKER_PROCESSES="${KONG_NGINX_WORKER_PROCESSES:-auto}"
export KONG_NGINX_WORKER_CONNECTIONS="${KONG_NGINX_WORKER_CONNECTIONS:-16384}"
export KONG_MEM_CACHE_SIZE="${KONG_MEM_CACHE_SIZE:-256m}"
export KONG_ADMIN_GUI_PATH="${KONG_ADMIN_GUI_PATH:-/}"
export KONG_ADMIN_GUI_AUTH="${KONG_ADMIN_GUI_AUTH:-off}"
export KONG_ENFORCE_RBAC="${KONG_ENFORCE_RBAC:-off}"
export KONG_ADMIN_GUI_SESSION_CONF="${KONG_ADMIN_GUI_SESSION_CONF:-}"
export KONG_ANONYMOUS_REPORTS="${KONG_ANONYMOUS_REPORTS:-off}"
export KONG_ADMIN_GUI_ACCESS_LOG="${KONG_ADMIN_GUI_ACCESS_LOG:-/dev/stdout}"
export KONG_ADMIN_GUI_CORS_ORIGINS="${KONG_ADMIN_GUI_CORS_ORIGINS:-*}"
export KONG_ADMIN_GUI_ERROR_LOG="${KONG_ADMIN_GUI_ERROR_LOG:-/dev/stderr}"
export KONG_PROXY_ACCESS_LOG="${KONG_PROXY_ACCESS_LOG:-/dev/stdout}"
export KONG_PROXY_ERROR_LOG="${KONG_PROXY_ERROR_LOG:-/dev/stderr}"
export KONG_ADMIN_ACCESS_LOG="${KONG_ADMIN_ACCESS_LOG:-/dev/stdout}"
export KONG_ADMIN_ERROR_LOG="${KONG_ADMIN_ERROR_LOG:-/dev/stderr}"
export KONG_LOG_LEVEL="${KONG_LOG_LEVEL:-notice}"
export KONG_USER="${KONG_USER:-kong}"
export KONG_HEALTHCHECK_INTERVAL="${KONG_HEALTHCHECK_INTERVAL:-10s}"
export KONG_HEALTHCHECK_TIMEOUT="${KONG_HEALTHCHECK_TIMEOUT:-10s}"
export KONG_HEALTHCHECK_RETRIES="${KONG_HEALTHCHECK_RETRIES:-10}"

# Configure trusted IPs for reverse proxy (Cloudflare IPs)
export KONG_TRUSTED_IPS="${KONG_TRUSTED_IPS:-0.0.0.0/0,::/0}"
export KONG_REAL_IP_RECURSIVE="${KONG_REAL_IP_RECURSIVE:-on}"
export KONG_REAL_IP_HEADER="${KONG_REAL_IP_HEADER:-X-Forwarded-For}"

# Kong URL Configuration
export KONG_ADMIN_GUI_URL="${KONG_ADMIN_GUI_URL:-${KONG_DOMAIN}}"
export KONG_ADMIN_API_URI="${KONG_ADMIN_API_URI:-${API_DOMAIN}}"
export KONG_ADMIN_GUI_API_URL="${KONG_ADMIN_GUI_API_URL:-${API_DOMAIN}}"

# Kong Listen Addresses
export KONG_PROXY_LISTEN="${KONG_PROXY_LISTEN:-0.0.0.0:8000}"
export KONG_ADMIN_LISTEN="${KONG_ADMIN_LISTEN:-0.0.0.0:8001}"
export KONG_ADMIN_GUI_LISTEN="${KONG_ADMIN_GUI_LISTEN:-0.0.0.0:8002}"
export KONG_STATUS_LISTEN="${KONG_STATUS_LISTEN:-0.0.0.0:8100}"

# Kong Port Mappings
export KONG_PROXY_PORT="${KONG_PROXY_PORT:-8000}"
export KONG_ADMIN_GUI_PORT="${KONG_ADMIN_GUI_PORT:-8002}"



# Function to display configuration summary
display_config() {
    echo ""
    echo "=== Kong Robust Configuration Summary ==="
    echo "Kong Version: $KONG_VERSION"
    echo "PostgreSQL Version: $POSTGRES_VERSION"
    echo "Redis Version: $REDIS_VERSION"
    echo "Kong Domain: $KONG_DOMAIN"
    echo "API Domain: $API_DOMAIN"
    echo "Robust Features: ✅ Enabled"
    echo "============================================"
    echo ""
}

# Check if required tools are available
check_dependencies() {
    local missing_deps=()

    command -v docker >/dev/null 2>&1 || missing_deps+=("docker")
    command -v jq >/dev/null 2>&1 || missing_deps+=("jq")

    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "Missing required dependencies: ${missing_deps[*]}"
        log_info "Please install: ${missing_deps[*]}"
        return 1
    fi

    log_success "All dependencies available"
    return 0
}

# Wait for Docker Compose services to be healthy
wait_for_compose_health() {
    log_info "Waiting for Docker Compose services to be healthy..."

    local start_time=$(date +%s)

    while true; do
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))

        if [ $elapsed -ge $HEALTH_CHECK_TIMEOUT ]; then
            log_error "Docker Compose health check timeout after ${HEALTH_CHECK_TIMEOUT}s"
            log_error "Checking Kong logs for troubleshooting..."
            docker logs "${BASE_DIR}-kong-1" --tail 20 2>/dev/null || log_warning "Could not retrieve Kong logs"
            return 1
        fi

        # Check Kong container health status
        local kong_health=$(docker inspect --format='{{.State.Health.Status}}' "${BASE_DIR}-kong-1" 2>/dev/null || echo "unknown")

        case $kong_health in
            "healthy")
                log_success "Kong container is healthy"
                return 0
                ;;
            "starting")
                log_info "Kong container is starting... (${elapsed}s elapsed)"
                ;;
            "unhealthy")
                log_error "Kong container is unhealthy"
                return 1
                ;;
            "unknown")
                log_info "Kong container not found or no health check... (${elapsed}s elapsed)"
                ;;
            *)
                log_warning "Kong container health status: $kong_health (${elapsed}s elapsed)"
                ;;
        esac

        sleep 2
    done
}

# Kong health endpoint validation with exponential backoff
wait_for_kong_health() {
    log_info "Validating Kong health endpoints with exponential backoff..."

    local attempt=1

    while [ $attempt -le $MAX_ATTEMPTS ]; do
        # Calculate delay with exponential backoff
        local delay=$((BASE_DELAY * (2 ** (attempt - 1))))
        if [ $delay -gt $MAX_DELAY ]; then
            delay=$MAX_DELAY
        fi

        log_info "Health check attempt $attempt/$MAX_ATTEMPTS"

        # Check if Kong process is running inside container
        if docker exec "${BASE_DIR}-kong-1" kong health >/dev/null 2>&1; then
            log_success "Kong process is running"
        else
            log_warning "Kong process not ready (attempt $attempt)"
            if [ $attempt -lt $MAX_ATTEMPTS ]; then
                log_info "Waiting ${delay}s before next attempt..."
                sleep $delay
            fi
            attempt=$((attempt + 1))
            continue
        fi

        # Check Admin API response (only if port 8001 is exposed)
        if command -v curl >/dev/null 2>&1; then
            local status_code=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8001/status 2>/dev/null || echo "000")

            if [ "$status_code" != "200" ]; then
                log_warning "Admin API not ready, HTTP status: ${status_code} (attempt $attempt)"
                if [ $attempt -lt $MAX_ATTEMPTS ]; then
                    log_info "Waiting ${delay}s before next attempt..."
                    sleep $delay
                fi
                attempt=$((attempt + 1))
                continue
            fi

            log_success "Admin API is responding"
        else
            log_info "Curl not available, skipping Admin API direct test"
        fi

        # Verify database connectivity (only if curl is available)
        if command -v curl >/dev/null 2>&1; then
            if curl -sf http://localhost:8001/status | jq -e '.database.reachable == true' >/dev/null 2>&1; then
                log_success "Database is reachable"
            else
                log_warning "Database not reachable (attempt $attempt)"
                if [ $attempt -lt $MAX_ATTEMPTS ]; then
                    log_info "Waiting ${delay}s before next attempt..."
                    sleep $delay
                fi
                attempt=$((attempt + 1))
                continue
            fi

            # Check Kong Manager
            local manager_status
            manager_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8002 2>/dev/null || echo "000")

            if [ "$manager_status" = "200" ]; then
                log_success "Kong Manager is responding"
            else
                log_warning "Kong Manager not ready, HTTP status: ${manager_status} (attempt $attempt)"
                if [ $attempt -lt $MAX_ATTEMPTS ]; then
                    log_info "Waiting ${delay}s before next attempt..."
                    sleep $delay
                fi
                attempt=$((attempt + 1))
                continue
            fi
        else
            log_info "Curl not available, skipping database and Kong Manager tests"
        fi

        # All checks passed
        log_success "Kong is fully operational! (attempt $attempt)"
        return 0
    done

    log_error "Kong failed to become ready after $MAX_ATTEMPTS attempts"
    return 1
}

# Configure API Loopback for secure Admin API access
configure_api_loopback() {
    log_info "Configuring API Loopback for secure Admin API access..."

    # Create a temporary container to interact with Kong's Admin API
    docker run --rm --network ${BASE_DIR}_kong-net curlimages/curl:latest \
        curl -s -X POST http://kong:8001/services \
        -H "Content-Type: application/json" \
        -d '{"name":"admin-api","host":"kong","port":8001,"protocol":"http"}' > /dev/null || true

    # Create Admin API Route
    docker run --rm --network ${BASE_DIR}_kong-net curlimages/curl:latest \
        curl -s -X POST http://kong:8001/services/admin-api/routes \
        -H "Content-Type: application/json" \
        -d '{"paths":["/admin-api"],"strip_path":true}' > /dev/null || true

    # Add CORS plugin for Kong Manager
    docker run --rm --network ${BASE_DIR}_kong-net curlimages/curl:latest \
        curl -s -X POST http://kong:8001/services/admin-api/plugins \
        -H "Content-Type: application/json" \
        -d '{"name":"cors","config":{"origins":["*"],"methods":["GET","POST","PUT","DELETE","PATCH","OPTIONS"],"headers":["Accept","Accept-Version","Content-Length","Content-MD5","Content-Type","Date","X-Auth-Token","Authorization"],"exposed_headers":["X-Auth-Token"],"credentials":true,"max_age":3600}}' > /dev/null || true

    # Generate a reference API key for manual API access (optional)
    ADMIN_API_KEY="kong-admin-reference-$(date +%s)"

    log_success "API Loopback configured successfully!"
    log_info "Admin API Key: $ADMIN_API_KEY"

    # Test the API Loopback configuration
    log_info "Testing API Loopback configuration..."

    local api_test=$(docker run --rm --network ${BASE_DIR}_kong-net curlimages/curl:latest \
        curl -s http://kong:8000/admin-api/status 2>/dev/null || echo "failed")

    if echo "$api_test" | grep -q "database"; then
        log_success "✅ API Loopback is working correctly!"
    else
        log_error "❌ API Loopback test failed. Kong Manager may not work properly."
        log_info "Testing API Loopback response: $api_test"
    fi
}

# Enhanced Kong Manager validation with API connectivity tests
validate_kong_manager() {
    log_info "Running enhanced Kong Manager validation..."

    # Test Kong Manager interface
    log_info "Testing Kong Manager interface..."
    local manager_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8002 2>/dev/null || echo "000")

    if [ "$manager_status" = "200" ]; then
        log_success "Kong Manager interface: OK"
    else
        log_error "Kong Manager interface: HTTP $manager_status"
        return 1
    fi

    # Test Kong Manager configuration (kconfig.js)
    log_info "Testing Kong Manager configuration..."
    local kconfig_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8002/kconfig.js 2>/dev/null || echo "000")

    if [ "$kconfig_status" = "200" ]; then
        log_success "Kong Manager configuration: OK"

        # Check if the configuration contains the correct API URL
        local api_url=$(curl -s http://localhost:8002/kconfig.js 2>/dev/null | grep -o "ADMIN_API_URL.*" | cut -d"'" -f3 || echo "unknown")
        log_info "Kong Manager API URL: $api_url"

        if [ "$DEPLOYMENT_MODE" = "local" ]; then
            if echo "$api_url" | grep -q "localhost:8000/admin-api"; then
                log_success "Kong Manager configured for local development"
            else
                log_warning "Kong Manager may not be configured correctly for local development"
                log_info "Expected: http://localhost:8000/admin-api, Got: $api_url"
            fi
        fi
    else
        log_error "Kong Manager configuration: HTTP $kconfig_status"
        return 1
    fi

    # Test API Loopback connectivity from localhost
    log_info "Testing API Loopback connectivity..."
    local loopback_endpoints=(
        "/admin-api/status:API Loopback Status"
        "/admin-api/services:API Loopback Services"
        "/admin-api/routes:API Loopback Routes"
    )

    for endpoint_info in "${loopback_endpoints[@]}"; do
        local endpoint="${endpoint_info%%:*}"
        local description="${endpoint_info##*:}"

        local status_code=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:8000${endpoint}" 2>/dev/null || echo "000")

        if [ "$status_code" = "200" ]; then
            log_success "$description: OK"
        else
            log_warning "$description: HTTP $status_code"
        fi
    done

    # Test CORS headers for Kong Manager
    log_info "Testing CORS configuration for Kong Manager..."
    local cors_test=$(curl -s -H "Origin: http://localhost:8002" -H "Access-Control-Request-Method: GET" -H "Access-Control-Request-Headers: Content-Type" -X OPTIONS http://localhost:8000/admin-api/status 2>/dev/null || echo "failed")

    if [ "$cors_test" != "failed" ]; then
        log_success "CORS configuration: OK"
    else
        log_warning "CORS configuration may need attention"
    fi

    log_success "Kong Manager validation completed!"
    return 0
}

# Comprehensive Kong validation
validate_kong_comprehensive() {
    log_info "Running comprehensive Kong validation..."

    # Test Admin API endpoints
    log_info "Testing Admin API endpoints..."

    local endpoints=(
        "/status:Kong status"
        "/services:Services endpoint"
        "/routes:Routes endpoint"
        "/plugins:Plugins endpoint"
        "/consumers:Consumers endpoint"
    )

    for endpoint_info in "${endpoints[@]}"; do
        local endpoint="${endpoint_info%%:*}"
        local description="${endpoint_info##*:}"

        local status_code=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:8001${endpoint}" 2>/dev/null || echo "000")

        if [ "$status_code" = "200" ]; then
            log_success "$description: OK"
        else
            log_warning "$description: HTTP $status_code"
        fi
    done

    # Test Kong Manager
    log_info "Testing Kong Manager..."
    if curl -sf http://localhost:8002 >/dev/null 2>&1; then
        log_success "Kong Manager: OK"
    else
        log_warning "Kong Manager: Not accessible"
    fi

    # Display Kong configuration summary
    log_info "Kong configuration summary:"
    if curl -sf http://localhost:8001/status >/dev/null 2>&1; then
        local kong_version=$(curl -s http://localhost:8001/ 2>/dev/null | jq -r '.version // "unknown"' 2>/dev/null || echo "unknown")
        local db_status=$(curl -s http://localhost:8001/status 2>/dev/null | jq -r '.database.reachable // false' 2>/dev/null || echo "unknown")

        echo "  Kong Version: $kong_version"
        echo "  Database Status: $db_status"
        echo "  Admin API: http://localhost:8001"
        echo "  Kong Manager: http://localhost:8002"
    fi
}

# Create Docker Compose configuration with robust health checks
create_docker_compose() {
    cat > "$BASE_DIR/docker-compose.yml" << 'EOF'
x-kong-config:
  &kong-env
  KONG_DATABASE: ${KONG_DATABASE:-postgres}
  KONG_PG_DATABASE: ${KONG_PG_DATABASE:-kong}
  KONG_PG_HOST: db
  KONG_PG_USER: ${KONG_PG_USER:-kong}
  KONG_PG_PASSWORD_FILE: /run/secrets/kong_postgres_password

x-healthcheck-config:
  &default-healthcheck
  interval: 10s
  timeout: 10s
  retries: 10
  start_period: 30s

secrets:
  kong_postgres_password:
    file: ./secrets/kong_postgres_password

volumes:
  pg_data: {}
  redis_data: {}
  kong_prefix_vol:
    driver_opts:
      type: tmpfs
      device: tmpfs
  kong_tmp_vol:
    driver_opts:
      type: tmpfs
      device: tmpfs

networks:
  kong-net:
    driver: bridge

services:
  db:
    image: postgres:${POSTGRES_VERSION:-16}
    volumes:
      - pg_data:/var/lib/postgresql/data
    environment:
      POSTGRES_DB: ${KONG_PG_DATABASE:-kong}
      POSTGRES_USER: ${KONG_PG_USER:-kong}
      POSTGRES_PASSWORD_FILE: /run/secrets/kong_postgres_password
    secrets:
      - kong_postgres_password
    networks:
      - kong-net
    restart: always
    healthcheck:
      <<: *default-healthcheck
      test: ["CMD-SHELL", "pg_isready -d ${KONG_PG_DATABASE:-kong} -U ${KONG_PG_USER:-kong}"]
      start_period: 10s

  redis:
    image: redis:${REDIS_VERSION:-latest}
    volumes:
      - redis_data:/data
    networks:
      - kong-net
    restart: always
    healthcheck:
      <<: *default-healthcheck
      test: ["CMD", "redis-cli", "ping"]
      start_period: 5s

  kong-migrations:
    image: kong:${KONG_VERSION:-latest}
    command: kong migrations bootstrap
    environment:
      <<: *kong-env
    secrets:
      - kong_postgres_password
    depends_on:
      db:
        condition: service_healthy
    networks:
      - kong-net
    restart: on-failure

  kong:
    image: kong:${KONG_VERSION:-latest}
    environment:
      <<: *kong-env
      KONG_PREFIX: ${KONG_PREFIX:-/var/run/kong}
      KONG_NGINX_WORKER_PROCESSES: ${KONG_NGINX_WORKER_PROCESSES:-auto}
      KONG_NGINX_WORKER_CONNECTIONS: ${KONG_NGINX_WORKER_CONNECTIONS:-16384}
      KONG_MEM_CACHE_SIZE: ${KONG_MEM_CACHE_SIZE:-256m}
      KONG_PROXY_LISTEN: ${KONG_PROXY_LISTEN:-0.0.0.0:8000}
      KONG_ADMIN_LISTEN: ${KONG_ADMIN_LISTEN:-0.0.0.0:8001}
      KONG_ADMIN_GUI_LISTEN: ${KONG_ADMIN_GUI_LISTEN:-0.0.0.0:8002}
      KONG_ADMIN_GUI_URL: ${KONG_ADMIN_GUI_URL}
      KONG_ADMIN_API_URI: ${KONG_ADMIN_API_URI}
      KONG_ADMIN_GUI_API_URL: ${KONG_ADMIN_GUI_API_URL}
      KONG_STATUS_LISTEN: ${KONG_STATUS_LISTEN:-0.0.0.0:8100}
      KONG_ADMIN_GUI_PATH: ${KONG_ADMIN_GUI_PATH:-/}
      KONG_ADMIN_GUI_AUTH: ${KONG_ADMIN_GUI_AUTH:-off}
      KONG_ENFORCE_RBAC: ${KONG_ENFORCE_RBAC:-off}
      KONG_ADMIN_GUI_SESSION_CONF: ${KONG_ADMIN_GUI_SESSION_CONF:-}
      KONG_ANONYMOUS_REPORTS: ${KONG_ANONYMOUS_REPORTS:-off}
      KONG_ADMIN_GUI_ACCESS_LOG: ${KONG_ADMIN_GUI_ACCESS_LOG:-/dev/stdout}
      KONG_ADMIN_GUI_ERROR_LOG: ${KONG_ADMIN_GUI_ERROR_LOG:-/dev/stderr}
      KONG_PROXY_ACCESS_LOG: ${KONG_PROXY_ACCESS_LOG:-/dev/stdout}
      KONG_PROXY_ERROR_LOG: ${KONG_PROXY_ERROR_LOG:-/dev/stderr}
      KONG_ADMIN_ACCESS_LOG: ${KONG_ADMIN_ACCESS_LOG:-/dev/stdout}
      KONG_ADMIN_ERROR_LOG: ${KONG_ADMIN_ERROR_LOG:-/dev/stderr}
      KONG_LOG_LEVEL: ${KONG_LOG_LEVEL:-notice}
      # Reverse proxy and CORS configuration
      KONG_TRUSTED_IPS: ${KONG_TRUSTED_IPS:-0.0.0.0/0,::/0}
      KONG_REAL_IP_RECURSIVE: ${KONG_REAL_IP_RECURSIVE:-on}
      KONG_REAL_IP_HEADER: ${KONG_REAL_IP_HEADER:-X-Forwarded-For}
      # CORS configuration for Kong Manager
      KONG_ADMIN_GUI_CORS_ORIGINS: ${KONG_ADMIN_GUI_CORS_ORIGINS:-*}
      # Additional security headers for reverse proxy
      KONG_HEADERS: "Server, X-Kong-Response-Latency"
    ports:
      # Expose Kong Manager and Proxy for API Loopback pattern
      - "${KONG_PROXY_PORT:-8000}:8000"
      - "${KONG_ADMIN_GUI_PORT:-8002}:8002"
      # Expose Admin API for development (comment out for production)
      - "8001:8001"
    secrets:
      - kong_postgres_password
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
      kong-migrations:
        condition: service_completed_successfully
    networks:
      - kong-net
    volumes:
      - kong_prefix_vol:/var/run/kong
      - kong_tmp_vol:/tmp
    user: "${KONG_USER:-kong}"
    healthcheck:
      <<: *default-healthcheck
      test: ["CMD-SHELL", "kong health"]
      start_period: 60s
      retries: 15
    restart: always
    read_only: true
    security_opt:
      - no-new-privileges
EOF
}

# Check for existing Kong installation
check_existing_installation() {
    local has_containers=false
    local has_directory=false
    local has_volumes=false

    # Check for existing Kong containers
    if docker ps -a --format "table {{.Names}}" | grep -q "kong"; then
        has_containers=true
    fi

    # Check for existing Kong directory
    if [ -d "$BASE_DIR" ]; then
        has_directory=true
    fi

    # Check for existing Kong volumes
    if docker volume ls --format "table {{.Name}}" | grep -q "kong"; then
        has_volumes=true
    fi

    # If nothing exists, return early
    if [ "$has_containers" = false ] && [ "$has_directory" = false ] && [ "$has_volumes" = false ]; then
        log_info "No existing Kong installation found. Proceeding with fresh setup..."
        return 0
    fi

    # Display what was found
    echo ""
    log_warning "Existing Kong installation detected:"
    if [ "$has_containers" = true ]; then
        echo "  🐳 Kong containers found:"
        docker ps -a --format "table {{.Names}}\t{{.Status}}" | grep kong | sed 's/^/    /'
    fi
    if [ "$has_directory" = true ]; then
        echo "  📁 Kong directory found: $BASE_DIR/"
        if [ -f "$BASE_DIR/docker-compose.yml" ]; then
            echo "    - docker-compose.yml exists"
        fi
        if [ -f "$BASE_DIR/.env" ]; then
            echo "    - .env file exists"
        fi
        if [ -d "$BASE_DIR/secrets" ]; then
            echo "    - secrets directory exists"
        fi
    fi
    if [ "$has_volumes" = true ]; then
        echo "  💾 Kong volumes found:"
        docker volume ls --format "table {{.Name}}\t{{.Driver}}" | grep kong | sed 's/^/    /'
    fi

    echo ""
    echo "Please choose how to proceed:"
    echo "  1) 🔄 Keep existing data and restart services (recommended for updates)"
    echo "  2) 🧹 Clean containers only (keep volumes and config)"
    echo "  3) 🗑️  Clean everything except volumes (fresh config, keep data)"
    echo "  4) ⚠️  Clean everything including volumes (complete fresh start)"
    echo "  5) ❌ Cancel and exit"
    echo ""

    while true; do
        read -r -p "Enter your choice (1-5): " choice
        case $choice in
            1)
                log_info "Keeping existing data and restarting services..."
                handle_keep_existing
                return $?
                ;;
            2)
                log_info "Cleaning containers only..."
                handle_clean_containers_only
                return $?
                ;;
            3)
                log_info "Cleaning everything except volumes..."
                handle_clean_except_volumes
                return $?
                ;;
            4)
                log_warning "This will delete ALL Kong data including databases!"
                read -r -p "Are you sure? Type 'yes' to confirm: " confirm
                if [ "$confirm" = "yes" ]; then
                    log_info "Cleaning everything including volumes..."
                    handle_clean_everything
                    return $?
                else
                    log_info "Cancelled. Please choose another option."
                    continue
                fi
                ;;
            5)
                log_info "Setup cancelled by user."
                exit 0
                ;;
            *)
                log_error "Invalid choice. Please enter 1, 2, 3, 4, or 5."
                ;;
        esac
    done
}

# Handle option 1: Keep existing data and restart services
handle_keep_existing() {
    log_info "Preserving existing Kong installation..."

    # Check if directory exists, if not create it
    if [ ! -d "$BASE_DIR" ]; then
        mkdir -p "$BASE_DIR"
        log_info "Created Kong directory"
    fi

    # Change to Kong directory
    cd "$BASE_DIR"

    # Check if docker-compose.yml exists
    if [ ! -f "docker-compose.yml" ]; then
        log_warning "docker-compose.yml not found. Creating new configuration..."
        cd ..
        create_docker_compose
        cd "$BASE_DIR"
    fi

    # Check if .env exists
    if [ ! -f ".env" ]; then
        log_warning ".env file not found. Creating new environment file..."
        cd ..
        create_env_file
        cd "$BASE_DIR"
    fi

    # Check if secrets exist
    if [ ! -f "secrets/kong_postgres_password" ]; then
        log_warning "Kong secrets not found. Creating new secrets..."
        mkdir -p secrets
        echo "$POSTGRES_PASSWORD" > "secrets/kong_postgres_password"
        log_info "Generated new PostgreSQL password: $POSTGRES_PASSWORD"
    else
        # Read existing password
        POSTGRES_PASSWORD=$(cat secrets/kong_postgres_password)
        log_info "Using existing PostgreSQL password"
    fi

    # Restart services gracefully
    log_info "Restarting Kong services..."
    docker compose restart || docker compose up -d

    return 0
}

# Handle option 2: Clean containers only
handle_clean_containers_only() {
    log_info "Stopping and removing Kong containers..."

    if [ -d "$BASE_DIR" ] && [ -f "$BASE_DIR/docker-compose.yml" ]; then
        cd "$BASE_DIR"
        docker compose down --remove-orphans || true
        cd ..
    else
        # Manual container cleanup
        docker ps -a --format "{{.Names}}" | grep kong | xargs -r docker rm -f || true
    fi

    log_success "Kong containers cleaned. Volumes and configuration preserved."
    return 0
}

# Handle option 3: Clean everything except volumes
handle_clean_except_volumes() {
    log_info "Cleaning Kong installation except volumes..."

    # Stop and remove containers
    if [ -d "$BASE_DIR" ] && [ -f "$BASE_DIR/docker-compose.yml" ]; then
        cd "$BASE_DIR"
        docker compose down --remove-orphans || true
        cd ..
    else
        docker ps -a --format "{{.Names}}" | grep kong | xargs -r docker rm -f || true
    fi

    # Remove Kong directory
    if [ -d "$BASE_DIR" ]; then
        rm -rf "$BASE_DIR"
        log_info "Removed Kong directory"
    fi

    # Remove Kong networks (but keep volumes)
    docker network ls --format "{{.Name}}" | grep kong | xargs -r docker network rm || true

    log_success "Kong installation cleaned. Volumes preserved for data recovery."
    return 0
}

# Handle option 4: Clean everything including volumes
handle_clean_everything() {
    log_info "Performing complete Kong cleanup..."

    # Stop and remove everything
    if [ -d "$BASE_DIR" ] && [ -f "$BASE_DIR/docker-compose.yml" ]; then
        cd "$BASE_DIR"
        docker compose down -v --remove-orphans || true
        cd ..
    else
        # Manual cleanup
        docker ps -a --format "{{.Names}}" | grep kong | xargs -r docker rm -f || true
        docker volume ls --format "{{.Name}}" | grep kong | xargs -r docker volume rm || true
        docker network ls --format "{{.Name}}" | grep kong | xargs -r docker network rm || true
    fi

    # Remove Kong directory
    if [ -d "$BASE_DIR" ]; then
        rm -rf "$BASE_DIR"
        log_info "Removed Kong directory"
    fi

    log_success "Complete Kong cleanup finished."
    return 0
}

# Create environment file (extracted for reuse)
create_env_file() {
    cat > "$BASE_DIR/.env" << EOF
KONG_VERSION=${KONG_VERSION}
POSTGRES_VERSION=${POSTGRES_VERSION}
REDIS_VERSION=${REDIS_VERSION}
KONG_DATABASE=${KONG_DATABASE}
KONG_PG_DATABASE=${KONG_PG_DATABASE}
KONG_PG_USER=${KONG_PG_USER}
KONG_PREFIX=${KONG_PREFIX}
KONG_NGINX_WORKER_PROCESSES=${KONG_NGINX_WORKER_PROCESSES}
KONG_NGINX_WORKER_CONNECTIONS=${KONG_NGINX_WORKER_CONNECTIONS}
KONG_MEM_CACHE_SIZE=${KONG_MEM_CACHE_SIZE}
KONG_PROXY_LISTEN=${KONG_PROXY_LISTEN}
KONG_ADMIN_LISTEN=${KONG_ADMIN_LISTEN}
KONG_ADMIN_GUI_LISTEN=${KONG_ADMIN_GUI_LISTEN}
KONG_ADMIN_GUI_URL=${KONG_ADMIN_GUI_URL}
KONG_ADMIN_API_URI=${KONG_ADMIN_API_URI}
KONG_ADMIN_GUI_API_URL=${KONG_ADMIN_GUI_API_URL}
KONG_STATUS_LISTEN=${KONG_STATUS_LISTEN}
KONG_ADMIN_GUI_PATH=${KONG_ADMIN_GUI_PATH}
KONG_ADMIN_GUI_AUTH=${KONG_ADMIN_GUI_AUTH}
KONG_ENFORCE_RBAC=${KONG_ENFORCE_RBAC}
KONG_ADMIN_GUI_SESSION_CONF=${KONG_ADMIN_GUI_SESSION_CONF}
KONG_ANONYMOUS_REPORTS=${KONG_ANONYMOUS_REPORTS}
KONG_ADMIN_GUI_ACCESS_LOG=${KONG_ADMIN_GUI_ACCESS_LOG}
KONG_ADMIN_GUI_ERROR_LOG=${KONG_ADMIN_GUI_ERROR_LOG}
KONG_PROXY_ACCESS_LOG=${KONG_PROXY_ACCESS_LOG}
KONG_PROXY_ERROR_LOG=${KONG_PROXY_ERROR_LOG}
KONG_ADMIN_ACCESS_LOG=${KONG_ADMIN_ACCESS_LOG}
KONG_ADMIN_ERROR_LOG=${KONG_ADMIN_ERROR_LOG}
KONG_LOG_LEVEL=${KONG_LOG_LEVEL}
KONG_USER=${KONG_USER}
KONG_HEALTHCHECK_INTERVAL=${KONG_HEALTHCHECK_INTERVAL}
KONG_HEALTHCHECK_TIMEOUT=${KONG_HEALTHCHECK_TIMEOUT}
KONG_HEALTHCHECK_RETRIES=${KONG_HEALTHCHECK_RETRIES}
KONG_TRUSTED_IPS=${KONG_TRUSTED_IPS}
KONG_REAL_IP_RECURSIVE=${KONG_REAL_IP_RECURSIVE}
KONG_REAL_IP_HEADER=${KONG_REAL_IP_HEADER}
KONG_PROXY_PORT=${KONG_PROXY_PORT}
KONG_ADMIN_GUI_PORT=${KONG_ADMIN_GUI_PORT}
KONG_ADMIN_GUI_CORS_ORIGINS=${KONG_ADMIN_GUI_CORS_ORIGINS}
EOF
}

# Main execution function
main() {
    # Display configuration
    display_config

    # Check dependencies
    if ! check_dependencies; then
        exit 1
    fi

    # Check for existing installation and handle user choice
    if ! check_existing_installation; then
        exit 1
    fi

    # If we reach here, either no existing installation was found,
    # or user chose to proceed with a clean setup

    # Create base directory
    mkdir -p "$BASE_DIR"

    # Create secrets directory and password file
    mkdir -p "$BASE_DIR/secrets"
    echo "$POSTGRES_PASSWORD" > "$BASE_DIR/secrets/kong_postgres_password"

    # Create Docker Compose configuration
    create_docker_compose

    # Create environment file for Docker Compose
    create_env_file

    # Change to base directory
    cd "$BASE_DIR"

    # Start all services with Docker Compose
    log_info "Starting Kong services with robust health checks..."
    docker compose up -d

    # Step 1: Wait for Docker Compose health checks
    log_info "Step 1: Docker Compose Health Validation"
    if ! wait_for_compose_health; then
        log_error "Docker Compose health check failed"
        exit 1
    fi

    # Step 2: Kong health endpoint validation with exponential backoff
    log_info "Step 2: Kong Health Endpoint Validation"
    if ! wait_for_kong_health; then
        log_error "Kong health validation failed"
        exit 1
    fi

    # Step 3: Configure API Loopback for secure Admin API access
    log_info "Step 3: API Loopback Configuration"
    configure_api_loopback

    # Step 4: Enhanced Kong Manager validation
    log_info "Step 4: Enhanced Kong Manager Validation"
    if ! validate_kong_manager; then
        log_warning "Kong Manager validation had issues, but continuing..."
    fi

    # Step 5: Comprehensive validation
    log_info "Step 5: Comprehensive Validation"
    validate_kong_comprehensive

    # Display final status
    log_info "Final container statuses:"
    docker compose ps

    echo ""
    log_success "Kong setup completed with robust features!"
    echo "PostgreSQL password: $POSTGRES_PASSWORD"
    echo ""
    echo "Access URLs:"
    echo "  Kong Manager: http://localhost:${KONG_ADMIN_GUI_PORT} (local) or ${KONG_DOMAIN} (external via tunnel)"
    echo "  Kong Admin API: ${API_DOMAIN} (secure external access via API Loopback)"
    echo "  Kong Admin API (internal): http://localhost:8001 (internal only)"
    echo "  Kong Proxy: http://localhost:${KONG_PROXY_PORT} (local) or ${KONG_DOMAIN} (external via tunnel)"
    echo ""
    echo "Security Configuration:"
    echo "  - Kong Manager authentication: ${KONG_ADMIN_GUI_AUTH}"
    echo "  - Anonymous reporting: ${KONG_ANONYMOUS_REPORTS}"
    echo "  - Admin API: Internal access only (port 8001) - SECURE"
    echo "  - Admin API External: Via Kong Proxy (no additional auth for Kong Manager) - SECURE"
    echo "  - Kong Proxy: External access via Cloudflare tunnel"
    echo "  - Trusted IPs configured for reverse proxy"
    echo "  - SSL handled by Cloudflare tunnel (no internal SSL certificates needed)"
    echo ""
    echo "Robust Features Implemented:"
    echo "  ✅ Docker Compose health checks with proper dependencies"
    echo "  ✅ Exponential backoff for Kong readiness validation"
    echo "  ✅ Multi-endpoint health validation"
    echo "  ✅ Database connectivity verification"
    echo "  ✅ Comprehensive error reporting and logging"
    echo "  ✅ CORS configuration for Kong Manager"
    echo "  ✅ API Loopback for secure external access"
    echo "  ✅ User choice for handling existing installations"
    echo "  ✅ Automatic environment detection (local/production)"
    echo "  ✅ Enhanced Kong Manager validation and debugging"
    echo "  ✅ Kong Manager API connectivity verification"
    echo ""
    echo "Kong Manager Debugging Features:"
    echo "  🔧 Automatic URL configuration based on deployment mode"
    echo "  🔧 Kong Manager interface validation"
    echo "  🔧 API Loopback connectivity testing"
    echo "  🔧 CORS configuration verification"
    echo "  🔧 Configuration file (kconfig.js) validation"
}

# Run main function
main "$@"